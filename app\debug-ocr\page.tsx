"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { processImageWithOCR, type OCRR<PERSON>ult } from "@/lib/ocr-processor"
import { Upload, Loader2, AlertCircle, CheckCircle } from "lucide-react"

export default function DebugOCRPage() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [ocrResult, setOcrResult] = useState<OCRResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [useRealOCR, setUseRealOCR] = useState(true)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith("image/")) {
        setError("Please select a valid image file.")
        return
      }

      if (file.size > 10 * 1024 * 1024) {
        setError("Image file is too large. Please select a file under 10MB.")
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const imageData = e.target?.result as string
        setSelectedImage(imageData)
        setOcrResult(null)
        setError(null)
      }
      reader.onerror = () => {
        setError("Failed to read the image file. Please try again.")
      }
      reader.readAsDataURL(file)
    }
  }

  const processImage = async () => {
    if (!selectedImage) return

    setIsProcessing(true)
    setError(null)
    setOcrResult(null)

    try {
      console.log("🚀 Starting OCR processing with mode:", useRealOCR ? "Real OCR" : "Fallback Demo")
      const result = await processImageWithOCR(selectedImage, !useRealOCR)
      setOcrResult(result)
    } catch (err: any) {
      console.error("OCR processing failed:", err)
      setError(err.message || "Failed to process the image")
    } finally {
      setIsProcessing(false)
    }
  }

  const resetTest = () => {
    setSelectedImage(null)
    setOcrResult(null)
    setError(null)
    setIsProcessing(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">OCR Debug Tool</h1>
        <p className="text-gray-600">
          Upload an image to test OCR processing and see detailed debugging output in the browser console.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={useRealOCR}
                  onChange={(e) => setUseRealOCR(e.target.checked)}
                  className="rounded"
                />
                <span>Use Real OCR (uncheck for demo mode)</span>
              </label>
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
                className="flex-1"
              >
                <Upload className="mr-2 h-4 w-4" />
                Select Image
              </Button>
              <Button
                onClick={processImage}
                disabled={!selectedImage || isProcessing}
                className="flex-1"
              >
                {isProcessing ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Process Image
              </Button>
              <Button onClick={resetTest} variant="outline">
                Reset
              </Button>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>

        {/* Selected Image */}
        {selectedImage && (
          <Card>
            <CardHeader>
              <CardTitle>Selected Image</CardTitle>
            </CardHeader>
            <CardContent>
              <img
                src={selectedImage}
                alt="Selected for OCR"
                className="max-w-full h-auto max-h-96 mx-auto border rounded"
              />
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* OCR Results */}
        {ocrResult && (
          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>OCR Results Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Confidence:</strong> {ocrResult.confidence.toFixed(1)}%
                  </div>
                  <div>
                    <strong>Text Length:</strong> {ocrResult.text.length} characters
                  </div>
                  <div>
                    <strong>Product Name:</strong> {ocrResult.productName || "Not found"}
                  </div>
                  <div>
                    <strong>Barcode:</strong> {ocrResult.barcode || "Not found"}
                  </div>
                  <div>
                    <strong>Nutrition Fields:</strong> {Object.keys(ocrResult.nutritionFacts).length}
                  </div>
                  <div>
                    <strong>Ingredients:</strong> {ocrResult.ingredients.length}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Raw OCR Text</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded border max-h-64 overflow-y-auto">
                  {ocrResult.text}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Parsed Nutrition Facts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {Object.entries(ocrResult.nutritionFacts).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="font-medium">{key}:</span>
                      <span>{value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Parsed Ingredients</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  {ocrResult.ingredients.length > 0 ? (
                    <ul className="list-disc list-inside space-y-1">
                      {ocrResult.ingredients.map((ingredient, index) => (
                        <li key={index}>{ingredient}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500">No ingredients found</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-2">
            <p>1. Open your browser's developer console (F12) to see detailed debugging output</p>
            <p>2. Upload an image of a nutrition label</p>
            <p>3. Choose between real OCR or demo mode</p>
            <p>4. Click "Process Image" and watch the console for detailed logs</p>
            <p>5. Compare the extracted values with what you see on the actual label</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
